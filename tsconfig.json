{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "ESNext", "moduleResolution": "node", "strict": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "removeComments": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "noEmitOnError": false, "downlevelIteration": true, "noEmit": false, "emitDeclarationOnly": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.js"]}