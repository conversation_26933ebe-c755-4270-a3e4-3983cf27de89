# n8n通用MCP工作流框架设计

## 1. 架构概述

### 1.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   n8n Workflow │    │  MCP Adapter    │    │   MCP Tools     │
│                 │◄──►│                 │◄──►│                 │
│ - 流程控制      │    │ - 协议转换      │    │ - bilibili      │
│ - 条件判断      │    │ - 参数映射      │    │ - 其他工具      │
│ - 错误处理      │    │ - 状态管理      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 核心组件
1. **MCP配置管理器** - 动态加载MCP工具配置
2. **智能路由器** - 根据目标选择合适的工具链
3. **参数映射器** - 处理工具间的数据传递
4. **执行监控器** - 跟踪执行状态和错误处理
5. **用户交互界面** - 处理用户输入和确认

## 2. n8n节点设计

### 2.1 核心自定义节点

#### A. MCP工具配置节点 (MCP Config Node)
```javascript
// n8n-nodes-mcp-config.js
{
  "displayName": "MCP工具配置",
  "name": "mcpConfig",
  "group": ["mcp"],
  "version": 1,
  "description": "加载和管理MCP工具配置",
  "defaults": {
    "name": "MCP Config"
  },
  "inputs": ["main"],
  "outputs": ["main"],
  "properties": [
    {
      "displayName": "配置文件路径",
      "name": "configPath",
      "type": "string",
      "default": "./mcp-tools-config.json",
      "description": "MCP工具配置文件路径"
    },
    {
      "displayName": "工具过滤器",
      "name": "toolFilter",
      "type": "multiOptions",
      "options": [],
      "default": [],
      "description": "选择要加载的工具"
    }
  ]
}
```

#### B. 目标分析节点 (Goal Analyzer Node)
```javascript
// n8n-nodes-goal-analyzer.js
{
  "displayName": "目标分析器",
  "name": "goalAnalyzer",
  "group": ["mcp"],
  "version": 1,
  "description": "分析用户目标并生成执行计划",
  "defaults": {
    "name": "Goal Analyzer"
  },
  "inputs": ["main"],
  "outputs": ["main", "alternative"],
  "properties": [
    {
      "displayName": "用户目标",
      "name": "userGoal",
      "type": "string",
      "default": "",
      "placeholder": "例如：上传视频到B站",
      "description": "描述您想要实现的目标"
    },
    {
      "displayName": "分析模式",
      "name": "analysisMode",
      "type": "options",
      "options": [
        {"name": "智能分析", "value": "intelligent"},
        {"name": "关键词匹配", "value": "keyword"},
        {"name": "手动选择", "value": "manual"}
      ],
      "default": "intelligent"
    },
    {
      "displayName": "置信度阈值",
      "name": "confidenceThreshold",
      "type": "number",
      "default": 0.7,
      "description": "分析结果的最低置信度"
    }
  ]
}
```

#### C. MCP工具执行节点 (MCP Tool Executor Node)
```javascript
// n8n-nodes-mcp-executor.js
{
  "displayName": "MCP工具执行器",
  "name": "mcpExecutor",
  "group": ["mcp"],
  "version": 1,
  "description": "执行指定的MCP工具",
  "defaults": {
    "name": "MCP Executor"
  },
  "inputs": ["main"],
  "outputs": ["main", "error"],
  "properties": [
    {
      "displayName": "工具名称",
      "name": "toolName",
      "type": "string",
      "default": "",
      "description": "要执行的MCP工具名称"
    },
    {
      "displayName": "参数映射",
      "name": "parameterMapping",
      "type": "fixedCollection",
      "typeOptions": {
        "multipleValues": true
      },
      "default": {},
      "options": [
        {
          "name": "parameters",
          "displayName": "参数",
          "values": [
            {
              "displayName": "参数名",
              "name": "name",
              "type": "string",
              "default": ""
            },
            {
              "displayName": "参数值",
              "name": "value",
              "type": "string",
              "default": ""
            },
            {
              "displayName": "数据来源",
              "name": "source",
              "type": "options",
              "options": [
                {"name": "用户输入", "value": "user_input"},
                {"name": "前序工具输出", "value": "previous_output"},
                {"name": "共享数据", "value": "shared_data"},
                {"name": "固定值", "value": "fixed_value"}
              ],
              "default": "user_input"
            }
          ]
        }
      ]
    },
    {
      "displayName": "重试配置",
      "name": "retryConfig",
      "type": "collection",
      "default": {},
      "options": [
        {
          "displayName": "最大重试次数",
          "name": "maxRetries",
          "type": "number",
          "default": 3
        },
        {
          "displayName": "重试延迟(秒)",
          "name": "retryDelay",
          "type": "number",
          "default": 2
        }
      ]
    }
  ]
}
```

### 2.2 工作流模板

#### A. 通用MCP执行工作流
```json
{
  "name": "通用MCP工作流模板",
  "nodes": [
    {
      "parameters": {
        "configPath": "./mcp-bilibili-tools.json"
      },
      "name": "加载MCP配置",
      "type": "mcpConfig",
      "position": [240, 300]
    },
    {
      "parameters": {
        "userGoal": "={{ $json.goal }}",
        "analysisMode": "intelligent"
      },
      "name": "分析目标",
      "type": "goalAnalyzer",
      "position": [460, 300]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{ $json.confidence }}",
              "operation": "larger",
              "value2": 0.7
            }
          ]
        }
      },
      "name": "检查置信度",
      "type": "n8n-nodes-base.if",
      "position": [680, 300]
    },
    {
      "parameters": {
        "mode": "runOnceForEachItem",
        "jsCode": "// 动态生成执行计划\nconst executionPlan = $input.first().json.execution_plan;\nconst results = [];\n\nfor (const step of executionPlan) {\n  results.push({\n    step_name: step.tool_name,\n    tool_config: step,\n    status: 'pending'\n  });\n}\n\nreturn results;"
      },
      "name": "生成执行步骤",
      "type": "n8n-nodes-base.code",
      "position": [900, 300]
    },
    {
      "parameters": {
        "toolName": "={{ $json.step_name }}",
        "parameterMapping": "={{ $json.tool_config.parameters }}"
      },
      "name": "执行MCP工具",
      "type": "mcpExecutor",
      "position": [1120, 300]
    }
  ],
  "connections": {
    "加载MCP配置": {
      "main": [["分析目标"]]
    },
    "分析目标": {
      "main": [["检查置信度"]]
    },
    "检查置信度": {
      "main": [["生成执行步骤"]]
    },
    "生成执行步骤": {
      "main": [["执行MCP工具"]]
    }
  }
}
```

## 3. MCP适配器实现

### 3.1 n8n MCP适配器服务
```javascript
// mcp-adapter-service.js
const express = require('express');
const { spawn } = require('child_process');
const fs = require('fs').promises;

class MCPAdapterService {
  constructor() {
    this.app = express();
    this.mcpProcesses = new Map();
    this.toolConfigs = new Map();
    this.setupRoutes();
  }

  async loadToolConfig(configPath) {
    try {
      const configData = await fs.readFile(configPath, 'utf8');
      const tools = JSON.parse(configData);
      
      for (const tool of tools) {
        this.toolConfigs.set(tool.name, tool);
      }
      
      return { success: true, toolCount: tools.length };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async analyzeGoal(goal, userInputs = {}) {
    const analyzer = new GoalAnalyzer(this.toolConfigs);
    return analyzer.analyze(goal, userInputs);
  }

  async executeTool(toolName, parameters) {
    const toolConfig = this.toolConfigs.get(toolName);
    if (!toolConfig) {
      throw new Error(`未知工具: ${toolName}`);
    }

    // 验证前置依赖
    for (const prerequisite of toolConfig.prerequisiteTools || []) {
      if (!this.isPrerequisiteSatisfied(prerequisite, parameters)) {
        throw new Error(`前置依赖未满足: ${prerequisite}`);
      }
    }

    // 验证必需参数
    const requiredParams = toolConfig.inputSchema.required || [];
    for (const param of requiredParams) {
      if (!(param in parameters)) {
        throw new Error(`缺少必需参数: ${param}`);
      }
    }

    // 执行MCP工具
    return this.callMCPTool(toolName, parameters);
  }

  async callMCPTool(toolName, parameters) {
    return new Promise((resolve, reject) => {
      const mcpProcess = spawn('npx', ['@mcpcn/mcp-bilibili'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      const request = {
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: parameters
        }
      };

      mcpProcess.stdin.write(JSON.stringify(request) + '\n');
      mcpProcess.stdin.end();

      let output = '';
      mcpProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      mcpProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(output);
            resolve(result);
          } catch (error) {
            reject(new Error(`解析MCP输出失败: ${error.message}`));
          }
        } else {
          reject(new Error(`MCP工具执行失败，退出码: ${code}`));
        }
      });

      mcpProcess.on('error', (error) => {
        reject(new Error(`启动MCP进程失败: ${error.message}`));
      });
    });
  }

  setupRoutes() {
    this.app.use(express.json());

    // 加载工具配置
    this.app.post('/api/mcp/load-config', async (req, res) => {
      try {
        const { configPath } = req.body;
        const result = await this.loadToolConfig(configPath);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // 分析目标
    this.app.post('/api/mcp/analyze-goal', async (req, res) => {
      try {
        const { goal, userInputs } = req.body;
        const result = await this.analyzeGoal(goal, userInputs);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // 执行工具
    this.app.post('/api/mcp/execute-tool', async (req, res) => {
      try {
        const { toolName, parameters } = req.body;
        const result = await this.executeTool(toolName, parameters);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // 获取工具列表
    this.app.get('/api/mcp/tools', (req, res) => {
      const tools = Array.from(this.toolConfigs.values()).map(tool => ({
        name: tool.name,
        description: tool.descriptionChinese || tool.description,
        prerequisites: tool.prerequisiteTools || [],
        canRunIndependently: tool.canRunIndependently
      }));
      res.json(tools);
    });
  }

  start(port = 3001) {
    this.app.listen(port, () => {
      console.log(`MCP适配器服务启动在端口 ${port}`);
    });
  }
}

// 启动服务
const service = new MCPAdapterService();
service.start();
```

## 4. n8n自定义节点实现

### 4.1 MCP执行器节点完整实现
```javascript
// nodes/MCPExecutor/MCPExecutor.node.js
const { IExecuteFunctions } = require('n8n-workflow');
const axios = require('axios');

class MCPExecutor {
  description = {
    displayName: 'MCP工具执行器',
    name: 'mcpExecutor',
    group: ['mcp'],
    version: 1,
    description: '执行MCP工具并处理结果',
    defaults: {
      name: 'MCP Executor',
    },
    inputs: ['main'],
    outputs: ['main', 'error'],
    properties: [
      {
        displayName: '工具名称',
        name: 'toolName',
        type: 'string',
        default: '',
        required: true,
        description: '要执行的MCP工具名称',
      },
      {
        displayName: '参数配置',
        name: 'parameters',
        type: 'json',
        default: '{}',
        description: '工具执行参数（JSON格式）',
      },
      {
        displayName: 'MCP服务地址',
        name: 'mcpServiceUrl',
        type: 'string',
        default: 'http://localhost:3001',
        description: 'MCP适配器服务地址',
      },
      {
        displayName: '超时时间(秒)',
        name: 'timeout',
        type: 'number',
        default: 300,
        description: '工具执行超时时间',
      },
    ],
  };

  async execute(context) {
    const items = context.getInputData();
    const returnData = [];
    const errorData = [];

    for (let i = 0; i < items.length; i++) {
      try {
        const toolName = context.getNodeParameter('toolName', i);
        const parametersJson = context.getNodeParameter('parameters', i);
        const mcpServiceUrl = context.getNodeParameter('mcpServiceUrl', i);
        const timeout = context.getNodeParameter('timeout', i);

        // 解析参数
        let parameters;
        try {
          parameters = typeof parametersJson === 'string' 
            ? JSON.parse(parametersJson) 
            : parametersJson;
        } catch (error) {
          throw new Error(`参数解析失败: ${error.message}`);
        }

        // 合并来自前序节点的数据
        const inputData = items[i].json;
        parameters = { ...parameters, ...inputData };

        // 调用MCP适配器服务
        const response = await axios.post(
          `${mcpServiceUrl}/api/mcp/execute-tool`,
          {
            toolName,
            parameters,
          },
          {
            timeout: timeout * 1000,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        // 处理成功结果
        const result = response.data;
        returnData.push({
          json: {
            tool_name: toolName,
            success: true,
            result: result,
            execution_time: new Date().toISOString(),
            input_parameters: parameters,
          },
        });

      } catch (error) {
        // 处理错误
        errorData.push({
          json: {
            tool_name: context.getNodeParameter('toolName', i),
            success: false,
            error: error.message,
            error_type: error.response?.status ? 'http_error' : 'execution_error',
            execution_time: new Date().toISOString(),
          },
        });
      }
    }

    // 返回结果到不同的输出端口
    return [returnData, errorData];
  }
}

module.exports = { MCPExecutor };
```

## 5. 部署和配置

### 5.1 Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  n8n:
    image: n8nio/n8n:latest
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=password
      - WEBHOOK_URL=http://localhost:5678/
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./custom-nodes:/home/<USER>/.n8n/custom
      - ./mcp-configs:/home/<USER>/mcp-configs
    depends_on:
      - mcp-adapter

  mcp-adapter:
    build: ./mcp-adapter
    ports:
      - "3001:3001"
    volumes:
      - ./mcp-configs:/app/configs
    environment:
      - NODE_ENV=production

volumes:
  n8n_data:
```

### 5.2 安装和启动脚本
```bash
#!/bin/bash
# setup-n8n-mcp.sh

echo "🚀 设置n8n MCP工作流框架..."

# 1. 创建目录结构
mkdir -p n8n-mcp-framework/{custom-nodes,mcp-adapter,mcp-configs,workflows}
cd n8n-mcp-framework

# 2. 复制MCP工具配置
cp ../mcp-bilibili-tools.json mcp-configs/

# 3. 构建MCP适配器
cd mcp-adapter
npm init -y
npm install express axios child_process

# 4. 安装n8n自定义节点
cd ../custom-nodes
npm init -y
npm install n8n-workflow axios

# 5. 启动服务
cd ..
docker-compose up -d

echo "✅ n8n MCP框架部署完成！"
echo "🌐 访问 http://localhost:5678 开始使用"
echo "📚 MCP适配器API: http://localhost:3001/api/mcp"
```

## 6. 使用示例

### 6.1 B站视频投稿工作流
1. **触发器**: Webhook或手动触发
2. **目标分析**: 分析"上传视频到B站"目标
3. **认证检查**: 检查B站登录状态
4. **条件分支**: 根据认证状态选择路径
5. **文件上传**: 执行视频上传流程
6. **稿件提交**: 完成视频发布
7. **结果通知**: 发送完成通知

### 6.2 配置步骤
1. 导入工作流模板到n8n
2. 配置MCP工具路径和参数
3. 设置用户输入表单
4. 配置错误处理和重试逻辑
5. 测试和优化工作流

这个框架提供了完整的n8n集成方案，支持任意MCP工具的动态加载和执行！
