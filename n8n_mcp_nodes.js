// n8n自定义MCP节点实现
// 文件路径: ~/.n8n/custom/n8n-nodes-mcp/nodes/MCP/MCP.node.js

const { IExecuteFunctions, INodeExecutionData, INodeType, INodeTypeDescription } = require('n8n-workflow');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

class MCPNode implements INodeType {
  description: INodeTypeDescription = {
    displayName: 'MCP工具',
    name: 'mcp',
    icon: 'fa:cogs',
    group: ['transform'],
    version: 1,
    subtitle: '={{$parameter["operation"]}}',
    description: '执行MCP (Model Context Protocol) 工具',
    defaults: {
      name: 'MCP',
    },
    inputs: ['main'],
    outputs: ['main'],
    credentials: [],
    properties: [
      {
        displayName: '操作',
        name: 'operation',
        type: 'options',
        noDataExpression: true,
        options: [
          {
            name: '加载工具配置',
            value: 'loadConfig',
            description: '从配置文件加载MCP工具',
          },
          {
            name: '分析目标',
            value: 'analyzeGoal',
            description: '分析用户目标并生成执行计划',
          },
          {
            name: '执行工具',
            value: 'executeTool',
            description: '执行指定的MCP工具',
          },
          {
            name: '批量执行',
            value: 'batchExecute',
            description: '按依赖顺序批量执行多个工具',
          },
        ],
        default: 'executeTool',
      },
      
      // 加载配置相关参数
      {
        displayName: '配置文件路径',
        name: 'configPath',
        type: 'string',
        displayOptions: {
          show: {
            operation: ['loadConfig'],
          },
        },
        default: './mcp-tools-config.json',
        description: 'MCP工具配置文件的路径',
      },
      
      // 目标分析相关参数
      {
        displayName: '用户目标',
        name: 'userGoal',
        type: 'string',
        displayOptions: {
          show: {
            operation: ['analyzeGoal'],
          },
        },
        default: '',
        placeholder: '例如：上传视频到B站',
        description: '描述您想要实现的目标',
      },
      {
        displayName: '分析模式',
        name: 'analysisMode',
        type: 'options',
        displayOptions: {
          show: {
            operation: ['analyzeGoal'],
          },
        },
        options: [
          {
            name: '智能分析',
            value: 'intelligent',
            description: '使用AI分析目标并推荐工具',
          },
          {
            name: '关键词匹配',
            value: 'keyword',
            description: '基于关键词匹配相关工具',
          },
          {
            name: '文件类型匹配',
            value: 'filetype',
            description: '基于文件类型匹配工具',
          },
        ],
        default: 'intelligent',
      },
      
      // 工具执行相关参数
      {
        displayName: '工具名称',
        name: 'toolName',
        type: 'string',
        displayOptions: {
          show: {
            operation: ['executeTool'],
          },
        },
        default: '',
        description: '要执行的MCP工具名称',
      },
      {
        displayName: '工具参数',
        name: 'toolParameters',
        type: 'json',
        displayOptions: {
          show: {
            operation: ['executeTool', 'batchExecute'],
          },
        },
        default: '{}',
        description: '工具执行参数（JSON格式）',
      },
      
      // 批量执行相关参数
      {
        displayName: '执行计划',
        name: 'executionPlan',
        type: 'json',
        displayOptions: {
          show: {
            operation: ['batchExecute'],
          },
        },
        default: '[]',
        description: '工具执行计划（JSON数组格式）',
      },
      
      // 通用配置
      {
        displayName: 'MCP服务地址',
        name: 'mcpServiceUrl',
        type: 'string',
        default: 'http://localhost:3001',
        description: 'MCP适配器服务的地址',
      },
      {
        displayName: '超时时间(秒)',
        name: 'timeout',
        type: 'number',
        default: 300,
        description: '工具执行的超时时间',
      },
      {
        displayName: '重试次数',
        name: 'maxRetries',
        type: 'number',
        default: 3,
        description: '失败时的最大重试次数',
      },
      {
        displayName: '详细日志',
        name: 'verboseLogging',
        type: 'boolean',
        default: false,
        description: '是否输出详细的执行日志',
      },
    ],
  };

  async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
    const items = this.getInputData();
    const returnData: INodeExecutionData[] = [];
    const operation = this.getNodeParameter('operation', 0) as string;

    for (let i = 0; i < items.length; i++) {
      try {
        let result;
        
        switch (operation) {
          case 'loadConfig':
            result = await this.loadConfig(i);
            break;
          case 'analyzeGoal':
            result = await this.analyzeGoal(i);
            break;
          case 'executeTool':
            result = await this.executeTool(i);
            break;
          case 'batchExecute':
            result = await this.batchExecute(i);
            break;
          default:
            throw new Error(`未知操作: ${operation}`);
        }

        returnData.push({
          json: {
            operation,
            success: true,
            result,
            timestamp: new Date().toISOString(),
            inputData: items[i].json,
          },
        });

      } catch (error) {
        const verboseLogging = this.getNodeParameter('verboseLogging', i) as boolean;
        
        if (verboseLogging) {
          console.error(`MCP节点执行错误:`, error);
        }

        returnData.push({
          json: {
            operation,
            success: false,
            error: error.message,
            errorType: error.name || 'UnknownError',
            timestamp: new Date().toISOString(),
            inputData: items[i].json,
          },
        });
      }
    }

    return [returnData];
  }

  private async loadConfig(itemIndex: number): Promise<any> {
    const configPath = this.getNodeParameter('configPath', itemIndex) as string;
    
    try {
      const configData = await fs.readFile(configPath, 'utf8');
      const tools = JSON.parse(configData);
      
      return {
        configPath,
        toolCount: tools.length,
        tools: tools.map((tool: any) => ({
          name: tool.name,
          description: tool.descriptionChinese || tool.description,
          prerequisites: tool.prerequisiteTools || [],
          canRunIndependently: tool.canRunIndependently,
        })),
      };
    } catch (error) {
      throw new Error(`加载配置文件失败: ${error.message}`);
    }
  }

  private async analyzeGoal(itemIndex: number): Promise<any> {
    const userGoal = this.getNodeParameter('userGoal', itemIndex) as string;
    const analysisMode = this.getNodeParameter('analysisMode', itemIndex) as string;
    const mcpServiceUrl = this.getNodeParameter('mcpServiceUrl', itemIndex) as string;
    const timeout = this.getNodeParameter('timeout', itemIndex) as number;

    const inputData = this.getInputData()[itemIndex].json;
    
    try {
      const response = await axios.post(
        `${mcpServiceUrl}/api/mcp/analyze-goal`,
        {
          goal: userGoal,
          analysisMode,
          userInputs: inputData,
        },
        {
          timeout: timeout * 1000,
          headers: { 'Content-Type': 'application/json' },
        }
      );

      return response.data;
    } catch (error) {
      throw new Error(`目标分析失败: ${error.message}`);
    }
  }

  private async executeTool(itemIndex: number): Promise<any> {
    const toolName = this.getNodeParameter('toolName', itemIndex) as string;
    const toolParametersJson = this.getNodeParameter('toolParameters', itemIndex) as string;
    const mcpServiceUrl = this.getNodeParameter('mcpServiceUrl', itemIndex) as string;
    const timeout = this.getNodeParameter('timeout', itemIndex) as number;
    const maxRetries = this.getNodeParameter('maxRetries', itemIndex) as number;

    // 解析参数
    let toolParameters;
    try {
      toolParameters = typeof toolParametersJson === 'string' 
        ? JSON.parse(toolParametersJson) 
        : toolParametersJson;
    } catch (error) {
      throw new Error(`工具参数解析失败: ${error.message}`);
    }

    // 合并输入数据
    const inputData = this.getInputData()[itemIndex].json;
    const finalParameters = { ...toolParameters, ...inputData };

    // 执行工具（带重试机制）
    let lastError;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await axios.post(
          `${mcpServiceUrl}/api/mcp/execute-tool`,
          {
            toolName,
            parameters: finalParameters,
          },
          {
            timeout: timeout * 1000,
            headers: { 'Content-Type': 'application/json' },
          }
        );

        return {
          toolName,
          attempt,
          result: response.data,
          parameters: finalParameters,
        };

      } catch (error) {
        lastError = error;
        
        if (attempt < maxRetries) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    throw new Error(`工具执行失败（已重试${maxRetries}次）: ${lastError.message}`);
  }

  private async batchExecute(itemIndex: number): Promise<any> {
    const executionPlanJson = this.getNodeParameter('executionPlan', itemIndex) as string;
    const mcpServiceUrl = this.getNodeParameter('mcpServiceUrl', itemIndex) as string;
    const verboseLogging = this.getNodeParameter('verboseLogging', itemIndex) as boolean;

    // 解析执行计划
    let executionPlan;
    try {
      executionPlan = typeof executionPlanJson === 'string' 
        ? JSON.parse(executionPlanJson) 
        : executionPlanJson;
    } catch (error) {
      throw new Error(`执行计划解析失败: ${error.message}`);
    }

    const results = [];
    const sharedData = {};
    const inputData = this.getInputData()[itemIndex].json;

    for (let stepIndex = 0; stepIndex < executionPlan.length; stepIndex++) {
      const step = executionPlan[stepIndex];
      
      if (verboseLogging) {
        console.log(`执行步骤 ${stepIndex + 1}/${executionPlan.length}: ${step.toolName}`);
      }

      try {
        // 准备参数
        const stepParameters = {
          ...inputData,
          ...sharedData,
          ...step.parameters,
        };

        // 执行工具
        const response = await axios.post(
          `${mcpServiceUrl}/api/mcp/execute-tool`,
          {
            toolName: step.toolName,
            parameters: stepParameters,
          },
          {
            timeout: (step.timeout || 300) * 1000,
            headers: { 'Content-Type': 'application/json' },
          }
        );

        const stepResult = {
          stepIndex: stepIndex + 1,
          toolName: step.toolName,
          success: true,
          result: response.data,
          executionTime: new Date().toISOString(),
        };

        results.push(stepResult);

        // 更新共享数据
        if (response.data && typeof response.data === 'object') {
          Object.assign(sharedData, response.data);
        }

      } catch (error) {
        const stepResult = {
          stepIndex: stepIndex + 1,
          toolName: step.toolName,
          success: false,
          error: error.message,
          executionTime: new Date().toISOString(),
        };

        results.push(stepResult);

        // 根据错误处理策略决定是否继续
        if (step.continueOnError !== true) {
          throw new Error(`步骤 ${stepIndex + 1} 执行失败: ${error.message}`);
        }
      }
    }

    return {
      totalSteps: executionPlan.length,
      completedSteps: results.filter(r => r.success).length,
      failedSteps: results.filter(r => !r.success).length,
      results,
      sharedData,
    };
  }
}

module.exports = { MCPNode };

// 导出节点类型定义
module.exports.nodeTypes = {
  MCP: MCPNode,
};
