{"$schema": "http://json-schema.org/draft-07/schema#", "title": "通用MCP工作流配置", "description": "定义MCP工具集合和工作流规则的配置文件格式", "type": "object", "properties": {"metadata": {"type": "object", "properties": {"name": {"type": "string", "description": "工作流集合名称"}, "version": {"type": "string", "description": "版本号"}, "description": {"type": "string", "description": "工作流集合描述"}, "author": {"type": "string", "description": "作者"}}, "required": ["name", "version"]}, "tools": {"type": "array", "description": "工具配置列表", "items": {"$ref": "#/definitions/tool"}}, "workflows": {"type": "array", "description": "预定义工作流模板", "items": {"$ref": "#/definitions/workflow"}}, "execution_strategies": {"type": "object", "description": "执行策略配置", "properties": {"default_mode": {"type": "string", "enum": ["sequential", "parallel", "conditional"], "default": "sequential"}, "retry_policy": {"$ref": "#/definitions/retry_policy"}, "timeout_policy": {"$ref": "#/definitions/timeout_policy"}}}, "parameter_mapping": {"type": "object", "description": "参数映射规则", "additionalProperties": {"$ref": "#/definitions/parameter_mapping_rule"}}}, "required": ["metadata", "tools"], "definitions": {"tool": {"type": "object", "properties": {"name": {"type": "string", "description": "工具名称"}, "description": {"type": "string", "description": "工具描述"}, "descriptionChinese": {"type": "string", "description": "中文描述"}, "inputSchema": {"type": "object", "description": "输入参数模式"}, "outputSchema": {"type": "object", "description": "输出结果模式"}, "prerequisiteTools": {"type": "array", "items": {"type": "string"}, "description": "前置依赖工具"}, "supportedExtensions": {"type": "array", "items": {"type": "string"}, "description": "支持的文件扩展名"}, "canRunIndependently": {"type": "boolean", "description": "是否可独立运行"}, "isDangerous": {"type": "boolean", "description": "是否为危险操作"}, "platform": {"type": "string", "description": "支持的平台"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "工具标签"}, "category": {"type": "string", "description": "工具分类"}}, "required": ["name", "description", "inputSchema"]}, "workflow": {"type": "object", "properties": {"name": {"type": "string", "description": "工作流名称"}, "description": {"type": "string", "description": "工作流描述"}, "goal": {"type": "string", "description": "工作流目标"}, "steps": {"type": "array", "items": {"$ref": "#/definitions/workflow_step"}, "description": "工作流步骤"}, "input_template": {"type": "object", "description": "输入参数模板"}, "output_template": {"type": "object", "description": "输出结果模板"}}, "required": ["name", "goal", "steps"]}, "workflow_step": {"type": "object", "properties": {"name": {"type": "string", "description": "步骤名称"}, "tool": {"type": "string", "description": "使用的工具"}, "condition": {"type": "string", "description": "执行条件"}, "parameters": {"type": "object", "description": "参数配置"}, "on_success": {"type": "string", "description": "成功后的动作"}, "on_failure": {"type": "string", "description": "失败后的动作"}}, "required": ["name", "tool"]}, "retry_policy": {"type": "object", "properties": {"max_retries": {"type": "integer", "minimum": 0, "default": 3}, "retry_delay": {"type": "number", "minimum": 0, "default": 1.0}, "backoff_factor": {"type": "number", "minimum": 1, "default": 2.0}, "retry_on_errors": {"type": "array", "items": {"type": "string"}, "description": "需要重试的错误类型"}}}, "timeout_policy": {"type": "object", "properties": {"default_timeout": {"type": "number", "minimum": 0, "default": 300}, "tool_timeouts": {"type": "object", "additionalProperties": {"type": "number", "minimum": 0}}}}, "parameter_mapping_rule": {"type": "object", "properties": {"source": {"type": "string", "description": "参数来源"}, "source_path": {"type": "string", "description": "来源路径"}, "transform": {"type": "string", "description": "转换函数"}, "default_value": {"description": "默认值"}}, "required": ["source"]}}}