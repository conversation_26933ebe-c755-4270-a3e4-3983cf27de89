{
  "ID": null,
  "c_name": "百度文本翻译工具",
  "description": "Translate text using Baidu Translation API with support for 26 languages and automatic language detection",
  "descriptionChinese": "使用百度翻译API进行多语言文本翻译，支持26种语言互译，可自动检测源语言",
  "fullName": "mcp-baidu-translate--translate_text",
  "inputSchema": {
    "type": "object",
    "required": ["text", "to_lang"],
    "properties": {
      "text": {
        "type": "string",
        "description": "需要翻译的文本内容"
      },
      "from_lang": {
        "type": "string",
        "description": "源语言代码，例如：'en'表示英语，'zh'表示中文，留空则自动检测"
      },
      "to_lang": {
        "type": "string",
        "description": "目标语言代码，例如：'zh'表示中文，'en'表示英语"
      }
    }
  },
  "is_single_call": 1,
  "keywords": "翻译,多语言,文本处理,百度API,语言转换,国际化,中英翻译,自动检测",
  "multiFileType": 0,
  "name": "translate_text",
  "outputSchema": {
    "type": "object"
  },
  "platform": "all",
  "points": 2,
  "projectId": null,
  "projectUUId": "mcp-baidu-translate",
  "regex": null,
  "supportedExtensions": [],
  "canProcessDirectory": false,
  "canDirectExecute": false,
  "isDangerous": false,
  "canRunIndependently": true,
  "prerequisiteTools": [],
  "shouldExclude": false,
  "excludeReason": ""
}


以下是这个MCP工具JSON格式中每个参数的详细含义：
基础标识信息
ID: 工具在数据库中的唯一标识符，null表示尚未分配
c_name: 工具的中文名称，用于中文界面显示
name: 工具的英文名称，通常是函数名或方法名
fullName: 完整名称，格式为"项目名--工具名"，用于唯一标识
描述信息
description: 工具的英文描述，说明工具功能
descriptionChinese: 工具的中文描述，便于中文用户理解
参数模式
inputSchema: 输入参数的JSON Schema定义，包含：
type: 参数类型（通常为"object"）
required: 必需参数列表
properties: 各参数的详细定义（类型、描述等）
outputSchema: 输出结果的JSON Schema定义
功能特性
is_single_call: 是否为单次调用工具（1=是，0=否）
multiFileType: 多文件处理类型
0 = 单文件处理
1 = 不同类型多文件处理
2 = 批量同类文件处理
supportedExtensions: 支持的文件扩展名数组
canProcessDirectory: 是否可以处理目录（true/false）
执行特性
canDirectExecute: 是否可直接执行系统级操作（true/false）
isDangerous: 是否为危险操作，需要用户确认（true/false）
canRunIndependently: 是否可独立运行，决定是否在工具列表中单独展示（true/false）
依赖关系
prerequisiteTools: 前置依赖的MCP工具列表，必须先调用这些工具才能执行当前工具
shouldExclude: 是否应该从工具列表中排除（true/false）
excludeReason: 如果需要排除，说明排除的原因
平台与分类
platform: 支持的运行平台（"mac"/"windows"/"linux"/"all"）
keywords: 用于搜索和检索的关键词，逗号分隔
项目关联
projectId: 关联的项目ID
projectUUId: 项目的UUID标识符
其他属性
points: 工具的评分或权重
regex: 正则表达式模式（如果适用）
这个JSON格式设计用于：
工具库管理: 自动化分类和检索
用户界面: 展示工具信息和参数
安全控制: 标识危险操作和依赖关系
平台兼容: 确定工具的运行环境
智能推荐: 基于关键词和特性进行工具推荐
