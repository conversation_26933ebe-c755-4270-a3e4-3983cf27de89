# Step 6: 质量评估整合专家

## 🎯 System Prompt

```markdown
# 角色定义
你是专业的质量评估和标准化专家，精通工具评估体系和文档标准化。你的专长是综合多维度信息进行质量评估，并生成符合行业标准的最终文档。

# 核心任务
基于前面五个步骤的分析结果，进行综合质量评估，生成最终的标准化工具描述文档，确保输出结果的完整性、准确性和用户友好性。

# 前置输入
你将收到以下完整的分析链条结果：
- Step 1: 项目元信息
- Step 2: 代码结构信息
- Step 3: 环境依赖信息
- Step 4: 工具功能详细分析
- Step 5: 用户体验转换结果

# 评估维度
1. **功能完整性**：工具功能的完整程度和实用性
2. **技术质量**：代码质量、架构设计和实现水平
3. **用户体验**：易用性、学习成本和满意度
4. **文档质量**：描述准确性、示例完整性和指导清晰度
5. **市场价值**：工具的独特性和市场需求匹配度

# 质量评估标准
## 功能评估 (1-10分)
- 10分: 功能完整、独特且高度实用
- 8-9分: 功能完整、实用性强
- 6-7分: 功能基本完整、有一定实用性
- 4-5分: 功能不够完整或实用性一般
- 1-3分: 功能缺失或实用性差

## 技术评估 (1-10分)
- 10分: 代码优秀、架构合理、无明显问题
- 8-9分: 代码良好、架构合理、问题较少
- 6-7分: 代码可接受、架构基本合理
- 4-5分: 代码质量一般、存在一些问题
- 1-3分: 代码质量差、问题较多

## 用户体验评估 (1-10分)
- 10分: 极易使用、学习成本低、体验优秀
- 8-9分: 容易使用、学习成本适中
- 6-7分: 基本易用、需要一定学习
- 4-5分: 使用复杂、学习成本较高
- 1-3分: 难以使用、学习成本很高

# 输出格式要求
严格按照以下JSON格式输出最终标准化文档：

```json
{
  "step6_result": {
    "analysis_metadata": {
      "version": "3.0",
      "analyzed_at": "2024-08-01T12:00:00Z",
      "analysis_duration": "分析耗时",
      "confidence_level": "整体分析置信度0.0-1.0",
      "completeness_score": "完整性评分0.0-1.0"
    },
    "project_summary": {
      "name": "项目名称",
      "display_name": "显示名称",
      "category": "工具分类",
      "complexity_level": "simple|medium|complex",
      "maturity_level": "experimental|beta|stable|mature",
      "overall_quality_score": "综合质量评分1-10",
      "recommendation_status": "strongly_recommended|recommended|conditional|not_recommended",
      "key_strengths": ["主要优势列表"],
      "improvement_areas": ["改进建议列表"]
    },
    "tools_final": [
      {
        "tool_id": "最终工具标识",
        "basic_info": {
          "name_chinese": "工具中文名",
          "name_english": "tool_function_name",
          "display_name": "界面显示名称",
          "category": "工具分类",
          "tags": ["标签列表"]
        },
        "descriptions": {
          "description": "简洁专业的英文描述（面向开发者）",
          "description_chinese": "详细通俗的中文描述（面向普通用户）",
          "summary": "一句话功能总结",
          "value_proposition": "核心价值主张"
        },
        "input_schema": {
          "type": "object",
          "required": ["必填参数列表"],
          "properties": {
            "parameter_name": {
              "type": "参数类型",
              "description": "参数描述（中英文）",
              "example": "示例值",
              "constraints": "约束条件",
              "user_friendly_name": "用户友好的参数名"
            }
          }
        },
        "output_schema": {
          "type": "object",
          "properties": {
            "result_field": {
              "type": "返回类型",
              "description": "返回值描述"
            }
          }
        },
        "usage_examples": [
          {
            "title": "示例标题",
            "description": "示例说明",
            "input": "输入示例",
            "output": "输出示例",
            "scenario": "使用场景"
          }
        ],
        "quality_metrics": {
          "functionality_score": "功能性评分1-10",
          "usability_score": "易用性评分1-10",
          "reliability_score": "可靠性评分1-10",
          "performance_score": "性能评分1-10",
          "documentation_score": "文档质量评分1-10",
          "overall_score": "综合评分1-10"
        },
        "recommendation": {
          "should_include": true,
          "confidence": "推荐置信度0.0-1.0",
          "reason": "推荐理由",
          "target_users": ["目标用户群体"],
          "use_cases": ["主要使用场景"],
          "alternatives": ["替代方案"]
        },
        "technical_specs": {
          "complexity": "技术复杂度",
          "dependencies": ["依赖列表"],
          "platform_support": ["支持平台"],
          "performance_notes": "性能特点",
          "security_level": "安全级别"
        }
      }
    ],
    "installation_guide": {
      "quick_setup": {
        "steps": ["快速安装步骤"],
        "time_estimate": "预计时间",
        "difficulty": "安装难度"
      },
      "detailed_setup": {
        "prerequisites": ["前置条件"],
        "installation_steps": ["详细安装步骤"],
        "configuration": ["配置说明"],
        "verification": ["验证步骤"]
      },
      "troubleshooting": [
        {
          "issue": "常见问题",
          "solution": "解决方案",
          "prevention": "预防措施"
        }
      ]
    },
    "usage_recommendations": {
      "best_practices": ["最佳实践"],
      "common_patterns": ["常用模式"],
      "integration_suggestions": ["集成建议"],
      "workflow_examples": ["工作流示例"]
    },
    "quality_assessment": {
      "overall_assessment": {
        "strengths": ["项目优势"],
        "weaknesses": ["项目劣势"],
        "opportunities": ["改进机会"],
        "risks": ["潜在风险"]
      },
      "comparison_analysis": {
        "similar_tools": ["类似工具"],
        "competitive_advantages": ["竞争优势"],
        "unique_features": ["独特功能"]
      },
      "future_outlook": {
        "maintainability": "可维护性评估",
        "scalability": "可扩展性评估",
        "evolution_potential": "发展潜力评估"
      }
    },
    "final_recommendations": {
      "for_users": ["给用户的建议"],
      "for_developers": ["给开发者的建议"],
      "for_organizations": ["给组织的建议"]
    }
  }
}
```

# 评估指导原则
1. **客观公正**：基于事实进行评估，避免主观偏见
2. **全面综合**：考虑技术、用户、市场等多个维度
3. **实用导向**：重点关注实际使用价值
4. **前瞻性**：考虑工具的发展潜力和可持续性
5. **标准化**：确保输出格式的一致性和规范性

# 特殊评估规则
- 对于实验性工具，适当降低稳定性要求
- 对于企业级工具，提高安全性和可靠性要求
- 对于开源工具，考虑社区活跃度和维护状况
- 对于商业工具，评估成本效益比

# 质量控制检查
- 确保所有必填字段都有值
- 验证评分的合理性和一致性
- 检查描述的准确性和完整性
- 确认示例的可用性和代表性
- 验证推荐建议的可操作性

现在请基于前面五个步骤的完整分析结果，进行最终的质量评估和标准化整合。
```

## 🔧 调用示例

```python
import json
from datetime import datetime
from typing import Dict, List, Any

class Step6Executor:
    def __init__(self, api_key):
        self.client = AsyncOpenAI(api_key=api_key)
        self.system_prompt = """上面的System Prompt内容"""
        
        # 质量评估权重
        self.quality_weights = {
            'functionality': 0.25,
            'usability': 0.25,
            'reliability': 0.20,
            'performance': 0.15,
            'documentation': 0.15
        }
        
        # 推荐阈值
        self.recommendation_thresholds = {
            'strongly_recommended': 8.5,
            'recommended': 7.0,
            'conditional': 5.0,
            'not_recommended': 0.0
        }
    
    async def execute(self, context):
        """
        执行Step 6分析
        
        Args:
            context: {
                'step1_result': Step1结果,
                'step2_result': Step2结果,
                'step3_result': Step3结果,
                'step4_result': Step4结果,
                'step5_result': Step5结果,
                'analysis_start_time': 分析开始时间
            }
        
        Returns:
            dict: Step 6的最终结果
        """
        
        # 预处理：质量评估
        quality_assessment = self._perform_quality_assessment(context)
        
        # 构建用户输入
        user_input = self._build_user_input(context, quality_assessment)
        
        try:
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_input}
                ],
                temperature=0.1,  # 低温度确保一致性
                max_tokens=10000
            )
            
            result = self._parse_response(response.choices[0].message.content)
            
            # 后处理：添加计算字段
            result = self._add_computed_fields(result, context)
            
            return result
            
        except Exception as e:
            return self._get_fallback_result(e)
    
    def _perform_quality_assessment(self, context) -> Dict:
        """执行质量评估"""
        assessment = {
            'overall_scores': {},
            'tool_scores': [],
            'project_health': {}
        }
        
        # 从Step4获取技术评估
        step4 = context.get('step4_result', {})
        technical_assessment = step4.get('technical_assessment', {})
        
        # 从Step5获取用户体验评估
        step5 = context.get('step5_result', {})
        user_tools = step5.get('user_friendly_tools', [])
        
        # 计算整体评分
        assessment['overall_scores'] = {
            'code_quality': technical_assessment.get('overall_code_quality', 5),
            'architecture': technical_assessment.get('architecture_soundness', 5),
            'maintainability': technical_assessment.get('maintainability', 5),
            'user_experience': self._calculate_ux_score(user_tools)
        }
        
        # 计算每个工具的评分
        tools_analysis = step4.get('tools_detailed_analysis', [])
        for i, tool in enumerate(tools_analysis):
            tool_score = self._calculate_tool_score(tool, user_tools[i] if i < len(user_tools) else {})
            assessment['tool_scores'].append(tool_score)
        
        return assessment
    
    def _calculate_ux_score(self, user_tools: List[Dict]) -> float:
        """计算用户体验评分"""
        if not user_tools:
            return 5.0
        
        total_score = 0
        for tool in user_tools:
            ux_metrics = tool.get('user_experience_metrics', {})
            ease_of_use = ux_metrics.get('ease_of_use', 5)
            accessibility = ux_metrics.get('accessibility', 5)
            satisfaction = ux_metrics.get('satisfaction_potential', 5)
            
            tool_ux_score = (ease_of_use + accessibility + satisfaction) / 3
            total_score += tool_ux_score
        
        return total_score / len(user_tools)
    
    def _calculate_tool_score(self, technical_analysis: Dict, user_analysis: Dict) -> Dict:
        """计算单个工具的综合评分"""
        # 技术评分
        impl_analysis = technical_analysis.get('implementation_analysis', {})
        code_quality = impl_analysis.get('code_quality', {})
        
        functionality_score = code_quality.get('performance', 5)
        reliability_score = self._assess_reliability(impl_analysis)
        
        # 用户体验评分
        ux_metrics = user_analysis.get('user_experience_metrics', {})
        usability_score = ux_metrics.get('ease_of_use', 5)
        
        # 文档评分
        documentation_score = self._assess_documentation_quality(user_analysis)
        
        # 性能评分
        perf_chars = impl_analysis.get('performance_characteristics', {})
        performance_score = self._assess_performance(perf_chars)
        
        # 加权计算总分
        weighted_score = (
            functionality_score * self.quality_weights['functionality'] +
            usability_score * self.quality_weights['usability'] +
            reliability_score * self.quality_weights['reliability'] +
            performance_score * self.quality_weights['performance'] +
            documentation_score * self.quality_weights['documentation']
        )
        
        return {
            'functionality_score': functionality_score,
            'usability_score': usability_score,
            'reliability_score': reliability_score,
            'performance_score': performance_score,
            'documentation_score': documentation_score,
            'overall_score': round(weighted_score, 1)
        }
    
    def _assess_reliability(self, impl_analysis: Dict) -> float:
        """评估可靠性"""
        error_handling = impl_analysis.get('error_handling', {})
        
        # 基于错误处理质量评分
        if 'robust' in str(error_handling).lower():
            return 8.5
        elif 'good' in str(error_handling).lower():
            return 7.0
        elif 'basic' in str(error_handling).lower():
            return 5.5
        else:
            return 4.0
    
    def _assess_documentation_quality(self, user_analysis: Dict) -> float:
        """评估文档质量"""
        usage_guide = user_analysis.get('usage_guide', {})
        examples = user_analysis.get('practical_examples', [])
        
        score = 5.0  # 基础分
        
        # 有使用指南加分
        if usage_guide.get('sample_prompts'):
            score += 1.5
        
        # 有实用示例加分
        if examples:
            score += 1.5
        
        # 有故障排除指南加分
        if user_analysis.get('troubleshooting'):
            score += 1.0
        
        return min(score, 10.0)
    
    def _assess_performance(self, perf_chars: Dict) -> float:
        """评估性能"""
        exec_time = perf_chars.get('execution_time_estimate', '')
        memory_usage = perf_chars.get('memory_usage', '')
        
        score = 7.0  # 默认分数
        
        # 基于执行时间调整
        if 'fast' in exec_time.lower() or '1-3' in exec_time:
            score += 1.0
        elif 'slow' in exec_time.lower():
            score -= 1.0
        
        # 基于内存使用调整
        if 'low' in memory_usage.lower():
            score += 0.5
        elif 'high' in memory_usage.lower():
            score -= 0.5
        
        return min(max(score, 1.0), 10.0)
    
    def _determine_recommendation(self, overall_score: float) -> str:
        """确定推荐状态"""
        for status, threshold in self.recommendation_thresholds.items():
            if overall_score >= threshold:
                return status
        return 'not_recommended'
    
    def _build_user_input(self, context, quality_assessment):
        """构建用户输入"""
        input_parts = [
            "# MCP工具最终质量评估和标准化整合",
            "",
            "## 质量评估预处理结果",
            json.dumps(quality_assessment, indent=2, ensure_ascii=False),
            "",
            "## 完整分析链条结果",
            ""
        ]
        
        # 添加所有步骤的结果
        for step_name, step_result in context.items():
            if step_name.startswith('step') and step_result:
                input_parts.extend([
                    f"### {step_name.upper()}",
                    json.dumps(step_result, indent=2, ensure_ascii=False)[:4000],  # 限制长度
                    ""
                ])
        
        input_parts.extend([
            "## 整合要求",
            "请基于以上完整的分析结果，生成最终的标准化工具描述文档，确保：",
            "1. 综合所有维度的评估结果",
            "2. 生成准确的质量评分和推荐建议",
            "3. 提供完整的安装和使用指南",
            "4. 确保输出格式的标准化和规范性",
            ""
        ])
        
        return "\n".join(input_parts)
    
    def _add_computed_fields(self, result: Dict, context: Dict) -> Dict:
        """添加计算字段"""
        # 添加分析元数据
        if 'step6_result' in result:
            step6_result = result['step6_result']
            
            # 计算分析时长
            start_time = context.get('analysis_start_time', datetime.now())
            duration = datetime.now() - start_time
            
            step6_result['analysis_metadata']['analysis_duration'] = f"{duration.total_seconds():.1f}秒"
            step6_result['analysis_metadata']['analyzed_at'] = datetime.now().isoformat()
        
        return result
    
    def _get_fallback_result(self, error):
        """错误降级结果"""
        return {
            "step6_result": {
                "analysis_metadata": {
                    "version": "3.0",
                    "analyzed_at": datetime.now().isoformat(),
                    "error": str(error)
                },
                "project_summary": {
                    "name": "分析失败",
                    "overall_quality_score": 0,
                    "recommendation_status": "not_recommended"
                },
                "tools_final": [],
                "quality_assessment": {
                    "overall_assessment": {
                        "strengths": [],
                        "weaknesses": ["分析过程中出现错误"],
                        "opportunities": [],
                        "risks": ["无法完成质量评估"]
                    }
                }
            }
        }

# 使用示例
async def main():
    executor = Step6Executor("your-api-key")
    
    context = {
        'step1_result': {'project_metadata': {'name': 'mcp-baidu-translate'}},
        'step4_result': {'technical_assessment': {'overall_code_quality': 8}},
        'step5_result': {'user_friendly_tools': [{'user_experience_metrics': {'ease_of_use': 9}}]},
        'analysis_start_time': datetime.now()
    }
    
    result = await executor.execute(context)
    print(json.dumps(result, indent=2, ensure_ascii=False))
```

## 🎯 完整系统架构

现在我为您创建一个总控制器来协调所有6个步骤：
