# Step 4: 工具功能解析专家

## 🎯 System Prompt

```markdown
# 角色定义
你是专业的MCP工具功能分析专家，精通各种工具的技术实现和功能逻辑。你的专长是深度解析工具的输入输出schema、核心算法和错误处理机制。

# 核心任务
基于前面三个步骤的分析结果，深度解析每个MCP工具的功能实现、参数定义和技术特性，为用户友好转换提供详细的技术基础。

# 前置输入
你将收到以下前置分析结果：
- Step 1: 项目元信息
- Step 2: 代码结构信息  
- Step 3: 环境依赖信息
- 完整的源代码内容

# 分析重点
1. **Schema深度解析**：详细分析inputSchema和outputSchema
2. **功能逻辑分析**：理解工具的核心处理逻辑
3. **参数验证机制**：识别参数校验和错误处理
4. **性能特性评估**：分析执行时间和资源消耗
5. **安全性评估**：识别潜在的安全风险

# 分析方法
## Schema解析
- 解析每个参数的类型、约束和默认值
- 识别必填参数和可选参数的业务逻辑
- 分析参数间的依赖关系
- 评估schema的完整性和合理性

## 功能逻辑分析
- 追踪数据流和处理步骤
- 识别核心算法和业务规则
- 分析异步处理和并发控制
- 评估功能的复杂度和可靠性

## 错误处理评估
- 检查输入验证机制
- 分析异常捕获和处理
- 评估错误信息的用户友好性
- 识别潜在的失败点

# 输出格式要求
严格按照以下JSON格式输出：

```json
{
  "step4_result": {
    "tools_detailed_analysis": [
      {
        "tool_id": "工具唯一标识",
        "basic_info": {
          "name_en": "英文函数名",
          "display_name": "显示名称",
          "category": "工具分类",
          "complexity_level": "simple|medium|complex"
        },
        "functionality_analysis": {
          "core_purpose": "核心功能描述",
          "algorithm_type": "算法类型",
          "processing_steps": ["处理步骤列表"],
          "business_logic": "业务逻辑说明",
          "data_flow": "数据流描述"
        },
        "input_schema_analysis": {
          "schema_structure": {
            "type": "object",
            "required": ["必填参数列表"],
            "properties": {
              "parameter_name": {
                "type": "参数类型",
                "description": "参数描述",
                "constraints": "约束条件",
                "business_meaning": "业务含义",
                "validation_rules": ["验证规则"],
                "example_values": ["示例值"]
              }
            }
          },
          "parameter_analysis": [
            {
              "name": "参数名",
              "importance": "critical|important|optional",
              "user_friendliness": "用户友好程度评分1-10",
              "common_mistakes": ["常见错误"],
              "best_practices": ["最佳实践"]
            }
          ],
          "schema_quality": {
            "completeness_score": "完整性评分1-10",
            "clarity_score": "清晰度评分1-10",
            "usability_score": "可用性评分1-10"
          }
        },
        "output_schema_analysis": {
          "return_format": "返回格式描述",
          "success_structure": "成功返回结构",
          "error_structure": "错误返回结构",
          "data_types": ["返回数据类型"],
          "response_size_estimate": "响应大小估计"
        },
        "implementation_analysis": {
          "code_quality": {
            "readability": "代码可读性评分1-10",
            "maintainability": "可维护性评分1-10",
            "performance": "性能评分1-10"
          },
          "error_handling": {
            "input_validation": "输入验证完善程度",
            "exception_handling": "异常处理机制",
            "error_messages": "错误信息质量",
            "graceful_degradation": "优雅降级能力"
          },
          "security_analysis": {
            "input_sanitization": "输入清理",
            "data_exposure_risk": "数据泄露风险",
            "injection_vulnerabilities": "注入漏洞风险",
            "authentication_handling": "认证处理"
          },
          "performance_characteristics": {
            "execution_time_estimate": "执行时间估计",
            "memory_usage": "内存使用情况",
            "network_dependency": "网络依赖程度",
            "scalability": "可扩展性评估"
          }
        },
        "dependencies_analysis": {
          "external_services": ["外部服务依赖"],
          "internal_modules": ["内部模块依赖"],
          "configuration_requirements": ["配置要求"],
          "runtime_dependencies": ["运行时依赖"]
        },
        "usage_patterns": {
          "typical_use_cases": ["典型使用场景"],
          "parameter_combinations": ["常见参数组合"],
          "performance_bottlenecks": ["性能瓶颈"],
          "optimization_opportunities": ["优化机会"]
        }
      }
    ],
    "cross_tool_analysis": {
      "tool_relationships": ["工具间关系"],
      "shared_components": ["共享组件"],
      "consistency_issues": ["一致性问题"],
      "integration_opportunities": ["集成机会"]
    },
    "technical_assessment": {
      "overall_code_quality": "整体代码质量评分1-10",
      "architecture_soundness": "架构合理性评分1-10",
      "maintainability": "可维护性评分1-10",
      "extensibility": "可扩展性评分1-10"
    }
  }
}
```

# 分析指导原则
1. **技术准确性**：确保对代码逻辑的理解准确无误
2. **全面性**：覆盖功能、性能、安全等多个维度
3. **实用性**：重点关注影响用户体验的技术特性
4. **客观性**：基于代码事实进行评估，避免主观臆断
5. **前瞻性**：考虑未来的维护和扩展需求

# 特殊处理规则
- 对于复杂的嵌套schema，要逐层分析
- 识别隐式的参数依赖关系
- 评估异步操作的并发安全性
- 标记潜在的性能问题和优化点

# 分析优先级
1. 首先分析核心功能和主要参数
2. 然后深入分析错误处理和边界情况
3. 接着评估性能和安全特性
4. 最后进行跨工具的一致性分析

现在请基于前面三个步骤的结果，开始深度分析MCP工具的功能实现。
```

## 🔧 调用示例

```python
import json
import ast
from typing import Dict, List, Any

class Step4Executor:
    def __init__(self, api_key):
        self.client = AsyncOpenAI(api_key=api_key)
        self.system_prompt = """上面的System Prompt内容"""
    
    async def execute(self, context):
        """
        执行Step 4分析
        
        Args:
            context: {
                'step1_result': Step1的分析结果,
                'step2_result': Step2的分析结果,
                'step3_result': Step3的分析结果,
                'source_code': 完整源代码
            }
        
        Returns:
            dict: Step 4的分析结果
        """
        
        # 预处理：提取工具相关代码
        tool_code_analysis = self._extract_tool_code(context)
        
        # 构建用户输入
        user_input = self._build_user_input(context, tool_code_analysis)
        
        try:
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_input}
                ],
                temperature=0.1,
                max_tokens=6000
            )
            
            result = self._parse_response(response.choices[0].message.content)
            return result
            
        except Exception as e:
            return self._get_fallback_result(e)
    
    def _extract_tool_code(self, context) -> Dict:
        """提取工具相关代码片段"""
        step2_result = context.get('step2_result', {})
        source_code = context.get('source_code', {})
        
        tool_code = {}
        
        # 从Step2结果中获取工具信息
        tools_found = step2_result.get('code_structure', {}).get('tool_handlers', [])
        
        for tool in tools_found:
            tool_name = tool.get('tool_name')
            file_location = tool.get('file_location')
            
            if file_location in source_code:
                # 提取工具相关代码
                tool_code[tool_name] = {
                    'handler_code': self._extract_function_code(
                        source_code[file_location], 
                        tool.get('handler_function')
                    ),
                    'schema_definition': self._extract_schema_definition(
                        source_code[file_location],
                        tool_name
                    )
                }
        
        return tool_code
    
    def _extract_function_code(self, file_content: str, function_name: str) -> str:
        """提取函数代码"""
        lines = file_content.split('\n')
        function_lines = []
        in_function = False
        brace_count = 0
        
        for line in lines:
            if function_name in line and ('function' in line or 'async' in line or 'def' in line):
                in_function = True
                function_lines.append(line)
                brace_count += line.count('{') - line.count('}')
            elif in_function:
                function_lines.append(line)
                brace_count += line.count('{') - line.count('}')
                if brace_count <= 0 and ('}' in line or 'return' in line):
                    break
        
        return '\n'.join(function_lines)
    
    def _extract_schema_definition(self, file_content: str, tool_name: str) -> str:
        """提取schema定义"""
        lines = file_content.split('\n')
        schema_lines = []
        in_schema = False
        
        for line in lines:
            if tool_name.upper() in line and ('TOOL' in line or 'Schema' in line):
                in_schema = True
                schema_lines.append(line)
            elif in_schema:
                schema_lines.append(line)
                if '}' in line and line.strip().endswith(';'):
                    break
        
        return '\n'.join(schema_lines)
    
    def _analyze_schema_complexity(self, schema_text: str) -> Dict:
        """分析schema复杂度"""
        analysis = {
            'parameter_count': 0,
            'required_count': 0,
            'nested_objects': 0,
            'enum_fields': 0,
            'validation_rules': 0
        }
        
        # 简单的文本分析
        analysis['parameter_count'] = schema_text.count('"type":')
        analysis['required_count'] = schema_text.count('"required"')
        analysis['nested_objects'] = schema_text.count('"type": "object"')
        analysis['enum_fields'] = schema_text.count('"enum"')
        
        return analysis
    
    def _build_user_input(self, context, tool_code_analysis):
        """构建用户输入"""
        input_parts = [
            "# MCP工具功能深度分析",
            "",
            "## 前置分析结果汇总",
            "",
            "### Step 1 - 项目信息",
            json.dumps(context.get('step1_result', {}), indent=2, ensure_ascii=False),
            "",
            "### Step 2 - 代码结构",
            json.dumps(context.get('step2_result', {}), indent=2, ensure_ascii=False),
            "",
            "### Step 3 - 环境依赖",
            json.dumps(context.get('step3_result', {}), indent=2, ensure_ascii=False),
            "",
            "## 工具代码详细分析",
            ""
        ]
        
        # 添加每个工具的代码
        for tool_name, code_info in tool_code_analysis.items():
            input_parts.extend([
                f"### 工具: {tool_name}",
                "",
                "#### Schema定义",
                "```",
                code_info.get('schema_definition', ''),
                "```",
                "",
                "#### 处理函数代码",
                "```",
                code_info.get('handler_code', ''),
                "```",
                ""
            ])
        
        return "\n".join(input_parts)
    
    def _get_fallback_result(self, error):
        """错误降级结果"""
        return {
            "step4_result": {
                "tools_detailed_analysis": [],
                "technical_assessment": {
                    "error": str(error),
                    "overall_code_quality": 0,
                    "architecture_soundness": 0,
                    "maintainability": 0,
                    "extensibility": 0
                }
            }
        }

# 使用示例
async def main():
    executor = Step4Executor("your-api-key")
    
    context = {
        'step1_result': {
            'project_metadata': {
                'name': 'mcp-baidu-translate',
                'main_language': 'TypeScript'
            }
        },
        'step2_result': {
            'code_structure': {
                'tool_handlers': [
                    {
                        'tool_name': 'translate_text',
                        'handler_function': 'handleTranslateText',
                        'file_location': 'src/index.ts'
                    }
                ]
            }
        },
        'step3_result': {
            'environment_analysis': {
                'external_apis': [
                    {
                        'service_name': '百度翻译API',
                        'criticality': 'critical'
                    }
                ]
            }
        },
        'source_code': {
            'src/index.ts': '''
const TRANSLATE_TEXT_TOOL = {
  name: "translate_text",
  description: "使用百度翻译进行文本翻译",
  inputSchema: {
    type: "object",
    properties: {
      text: {
        type: "string",
        description: "需要翻译的文本内容"
      },
      to_lang: {
        type: "string", 
        description: "目标语言代码"
      }
    },
    required: ["text", "to_lang"]
  }
};

async function handleTranslateText(input) {
  if (!input.text) {
    return { isError: true, errorMessage: "翻译文本不能为空" };
  }
  
  try {
    const response = await fetch(API_ENDPOINT);
    return { content: [{ type: "text", text: result }] };
  } catch (error) {
    return { isError: true, errorMessage: error.message };
  }
}
            '''
        }
    }
    
    result = await executor.execute(context)
    print(json.dumps(result, indent=2, ensure_ascii=False))
```

## 📊 预期输出示例

```json
{
  "step4_result": {
    "tools_detailed_analysis": [
      {
        "tool_id": "translate_text_001",
        "basic_info": {
          "name_en": "translate_text",
          "display_name": "文本翻译工具",
          "category": "文本处理",
          "complexity_level": "medium"
        },
        "functionality_analysis": {
          "core_purpose": "调用百度翻译API进行多语言文本翻译",
          "algorithm_type": "API调用封装",
          "processing_steps": [
            "输入验证",
            "参数构建",
            "API调用",
            "结果处理",
            "错误处理"
          ],
          "business_logic": "基于百度翻译API的文本翻译服务"
        },
        "input_schema_analysis": {
          "schema_structure": {
            "type": "object",
            "required": ["text", "to_lang"],
            "properties": {
              "text": {
                "type": "string",
                "description": "需要翻译的文本内容",
                "business_meaning": "用户输入的源文本",
                "validation_rules": ["非空检查"],
                "example_values": ["Hello World", "你好世界"]
              },
              "to_lang": {
                "type": "string",
                "description": "目标语言代码",
                "business_meaning": "翻译目标语言",
                "validation_rules": ["语言代码格式"],
                "example_values": ["zh", "en", "ja"]
              }
            }
          },
          "schema_quality": {
            "completeness_score": 8,
            "clarity_score": 9,
            "usability_score": 8
          }
        },
        "implementation_analysis": {
          "code_quality": {
            "readability": 8,
            "maintainability": 7,
            "performance": 7
          },
          "error_handling": {
            "input_validation": "基础验证完善",
            "exception_handling": "有try-catch机制",
            "error_messages": "用户友好",
            "graceful_degradation": "良好"
          },
          "security_analysis": {
            "input_sanitization": "基础清理",
            "data_exposure_risk": "低",
            "injection_vulnerabilities": "低风险",
            "authentication_handling": "通过环境变量"
          }
        }
      }
    ],
    "technical_assessment": {
      "overall_code_quality": 8,
      "architecture_soundness": 8,
      "maintainability": 7,
      "extensibility": 7
    }
  }
}
```

这是Step 4的完整设计。接下来我继续设计Step 5和Step 6吗？
