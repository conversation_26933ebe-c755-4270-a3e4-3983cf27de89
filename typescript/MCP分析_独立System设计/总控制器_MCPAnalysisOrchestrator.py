#!/usr/bin/env python3
"""
MCP工具分析系统 - 总控制器
基于6个独立System Prompt的分步骤分析架构
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# 导入各个步骤的执行器
from step1_executor import Step1Executor
from step2_executor import Step2Executor  
from step3_executor import Step3Executor
from step4_executor import Step4Executor
from step5_executor import Step5Executor
from step6_executor import Step6Executor


class MCPAnalysisOrchestrator:
    """MCP工具分析系统总控制器"""
    
    def __init__(self, api_key: str, config: Optional[Dict] = None):
        """
        初始化分析系统
        
        Args:
            api_key: OpenAI/Anthropic API密钥
            config: 配置参数
        """
        self.api_key = api_key
        self.config = config or {}
        
        # 初始化各步骤执行器
        self.step_executors = {
            'step1': Step1Executor(api_key),
            'step2': Step2Executor(api_key),
            'step3': Step3Executor(api_key),
            'step4': Step4Executor(api_key),
            'step5': Step5Executor(api_key),
            'step6': Step6Executor(api_key)
        }
        
        # 分析状态
        self.analysis_state = {
            'start_time': None,
            'current_step': None,
            'completed_steps': [],
            'results': {},
            'errors': []
        }
    
    async def analyze_mcp_project(self, project_path: str) -> Dict[str, Any]:
        """
        分析MCP项目的主入口方法
        
        Args:
            project_path: MCP项目路径
            
        Returns:
            完整的分析结果
        """
        self.analysis_state['start_time'] = datetime.now()
        
        try:
            # 第一阶段：并行分析 (Steps 1-3)
            print("🔄 开始并行分析阶段 (Steps 1-3)...")
            parallel_results = await self._execute_parallel_phase(project_path)
            
            # 第二阶段：串行分析 (Steps 4-6)
            print("🔗 开始串行分析阶段 (Steps 4-6)...")
            final_result = await self._execute_serial_phase(parallel_results)
            
            # 生成分析报告
            analysis_report = self._generate_analysis_report(final_result)
            
            print("✅ MCP工具分析完成！")
            return analysis_report
            
        except Exception as e:
            print(f"❌ 分析过程中出现错误: {str(e)}")
            return self._generate_error_report(e)
    
    async def _execute_parallel_phase(self, project_path: str) -> Dict[str, Any]:
        """执行并行分析阶段 (Steps 1-3)"""
        
        # 预处理：读取项目文件
        project_data = await self._load_project_data(project_path)
        
        # 并行执行Steps 1-3
        tasks = [
            self._execute_step_with_retry('step1', project_data),
            self._execute_step_with_retry('step2', project_data),
            self._execute_step_with_retry('step3', project_data)
        ]
        
        step1_result, step2_result, step3_result = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        results = {
            'step1_result': step1_result if not isinstance(step1_result, Exception) else None,
            'step2_result': step2_result if not isinstance(step2_result, Exception) else None,
            'step3_result': step3_result if not isinstance(step3_result, Exception) else None,
            'project_data': project_data
        }
        
        # 记录错误
        for step_name, result in [('step1', step1_result), ('step2', step2_result), ('step3', step3_result)]:
            if isinstance(result, Exception):
                self.analysis_state['errors'].append({
                    'step': step_name,
                    'error': str(result),
                    'timestamp': datetime.now().isoformat()
                })
        
        return results
    
    async def _execute_serial_phase(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行串行分析阶段 (Steps 4-6)"""
        
        # Step 4: 工具功能解析
        print("  📊 执行Step 4: 工具功能解析...")
        step4_result = await self._execute_step_with_retry('step4', context)
        context['step4_result'] = step4_result
        
        # Step 5: 用户体验转换
        print("  🎨 执行Step 5: 用户体验转换...")
        step5_result = await self._execute_step_with_retry('step5', context)
        context['step5_result'] = step5_result
        
        # Step 6: 质量评估整合
        print("  🎯 执行Step 6: 质量评估整合...")
        step6_result = await self._execute_step_with_retry('step6', context)
        context['step6_result'] = step6_result
        
        return context
    
    async def _execute_step_with_retry(self, step_name: str, context: Any, max_retries: int = 3) -> Dict[str, Any]:
        """带重试机制的步骤执行"""
        
        self.analysis_state['current_step'] = step_name
        executor = self.step_executors[step_name]
        
        for attempt in range(max_retries):
            try:
                print(f"    ⚡ 执行{step_name.upper()} (尝试 {attempt + 1}/{max_retries})")
                
                result = await executor.execute(context)
                
                # 验证结果
                if self._validate_step_result(step_name, result):
                    self.analysis_state['completed_steps'].append(step_name)
                    self.analysis_state['results'][step_name] = result
                    return result
                else:
                    raise ValueError(f"{step_name}结果验证失败")
                    
            except Exception as e:
                print(f"    ⚠️ {step_name}执行失败 (尝试 {attempt + 1}): {str(e)}")
                
                if attempt == max_retries - 1:
                    # 最后一次尝试失败，返回降级结果
                    fallback_result = await self._get_step_fallback(step_name, e)
                    self.analysis_state['errors'].append({
                        'step': step_name,
                        'error': str(e),
                        'fallback_used': True
                    })
                    return fallback_result
                
                # 等待后重试
                await asyncio.sleep(2 ** attempt)
        
        return {}
    
    async def _load_project_data(self, project_path: str) -> Dict[str, Any]:
        """加载项目数据"""
        project_data = {
            'project_path': project_path,
            'config_files': {},
            'source_files': {},
            'documentation': {},
            'directory_structure': ''
        }
        
        project_dir = Path(project_path)
        
        if not project_dir.exists():
            raise FileNotFoundError(f"项目路径不存在: {project_path}")
        
        # 读取配置文件
        config_patterns = ['package.json', 'requirements.txt', 'pyproject.toml', 'go.mod', 'Cargo.toml']
        for pattern in config_patterns:
            config_file = project_dir / pattern
            if config_file.exists():
                project_data['config_files'][pattern] = config_file.read_text(encoding='utf-8')
        
        # 读取源代码文件
        source_patterns = ['**/*.js', '**/*.ts', '**/*.py', '**/*.go', '**/*.rs']
        for pattern in source_patterns:
            for source_file in project_dir.glob(pattern):
                if source_file.is_file() and 'node_modules' not in str(source_file):
                    relative_path = source_file.relative_to(project_dir)
                    try:
                        project_data['source_files'][str(relative_path)] = source_file.read_text(encoding='utf-8')
                    except UnicodeDecodeError:
                        # 跳过二进制文件
                        continue
        
        # 读取文档文件
        doc_patterns = ['README.md', 'README.rst', 'CHANGELOG.md', 'LICENSE']
        for pattern in doc_patterns:
            doc_file = project_dir / pattern
            if doc_file.exists():
                project_data['documentation'][pattern] = doc_file.read_text(encoding='utf-8')
        
        # 生成目录结构
        project_data['directory_structure'] = self._generate_directory_tree(project_dir)
        
        return project_data
    
    def _generate_directory_tree(self, path: Path, max_depth: int = 3) -> str:
        """生成目录树结构"""
        def _tree_generator(dir_path: Path, prefix: str = "", depth: int = 0):
            if depth > max_depth:
                return
            
            items = sorted(dir_path.iterdir(), key=lambda x: (x.is_file(), x.name))
            
            for i, item in enumerate(items):
                if item.name.startswith('.') or item.name in ['node_modules', '__pycache__', 'target']:
                    continue
                
                is_last = i == len(items) - 1
                current_prefix = "└── " if is_last else "├── "
                yield f"{prefix}{current_prefix}{item.name}"
                
                if item.is_dir() and depth < max_depth:
                    next_prefix = prefix + ("    " if is_last else "│   ")
                    yield from _tree_generator(item, next_prefix, depth + 1)
        
        tree_lines = list(_tree_generator(path))
        return "\n".join(tree_lines)
    
    def _validate_step_result(self, step_name: str, result: Dict[str, Any]) -> bool:
        """验证步骤结果的有效性"""
        if not result or not isinstance(result, dict):
            return False
        
        # 检查是否包含预期的结果键
        expected_keys = {
            'step1': 'step1_result',
            'step2': 'step2_result', 
            'step3': 'step3_result',
            'step4': 'step4_result',
            'step5': 'step5_result',
            'step6': 'step6_result'
        }
        
        expected_key = expected_keys.get(step_name)
        return expected_key in result
    
    async def _get_step_fallback(self, step_name: str, error: Exception) -> Dict[str, Any]:
        """获取步骤的降级结果"""
        fallback_results = {
            'step1': {'step1_result': {'project_metadata': {'name': 'unknown', 'error': str(error)}}},
            'step2': {'step2_result': {'code_structure': {'error': str(error)}}},
            'step3': {'step3_result': {'environment_analysis': {'error': str(error)}}},
            'step4': {'step4_result': {'tools_detailed_analysis': [], 'error': str(error)}},
            'step5': {'step5_result': {'user_friendly_tools': [], 'error': str(error)}},
            'step6': {'step6_result': {'project_summary': {'name': 'analysis_failed', 'error': str(error)}}}
        }
        
        return fallback_results.get(step_name, {})
    
    def _generate_analysis_report(self, final_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终分析报告"""
        end_time = datetime.now()
        duration = end_time - self.analysis_state['start_time']
        
        report = {
            'analysis_metadata': {
                'version': '3.0',
                'start_time': self.analysis_state['start_time'].isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration.total_seconds(),
                'completed_steps': self.analysis_state['completed_steps'],
                'errors_count': len(self.analysis_state['errors']),
                'success_rate': len(self.analysis_state['completed_steps']) / 6
            },
            'final_result': final_result.get('step6_result', {}),
            'intermediate_results': {
                'step1': final_result.get('step1_result'),
                'step2': final_result.get('step2_result'),
                'step3': final_result.get('step3_result'),
                'step4': final_result.get('step4_result'),
                'step5': final_result.get('step5_result')
            },
            'errors': self.analysis_state['errors']
        }
        
        return report
    
    def _generate_error_report(self, error: Exception) -> Dict[str, Any]:
        """生成错误报告"""
        return {
            'analysis_metadata': {
                'version': '3.0',
                'start_time': self.analysis_state['start_time'].isoformat() if self.analysis_state['start_time'] else None,
                'end_time': datetime.now().isoformat(),
                'success': False,
                'error': str(error)
            },
            'final_result': {},
            'intermediate_results': self.analysis_state['results'],
            'errors': self.analysis_state['errors'] + [{'fatal_error': str(error)}]
        }


# 使用示例
async def main():
    """主函数示例"""
    
    # 初始化分析器
    orchestrator = MCPAnalysisOrchestrator(
        api_key="your-api-key-here",
        config={
            'max_retries': 3,
            'timeout': 300,
            'parallel_execution': True
        }
    )
    
    # 分析MCP项目
    project_path = "/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-baidu-translate"
    
    try:
        result = await orchestrator.analyze_mcp_project(project_path)
        
        # 保存结果
        output_file = f"mcp_analysis_result_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"📄 分析结果已保存到: {output_file}")
        
        # 打印摘要
        final_result = result.get('final_result', {})
        project_summary = final_result.get('project_summary', {})
        
        print("\n📊 分析摘要:")
        print(f"项目名称: {project_summary.get('name', 'N/A')}")
        print(f"质量评分: {project_summary.get('overall_quality_score', 'N/A')}")
        print(f"推荐状态: {project_summary.get('recommendation_status', 'N/A')}")
        print(f"工具数量: {len(final_result.get('tools_final', []))}")
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
